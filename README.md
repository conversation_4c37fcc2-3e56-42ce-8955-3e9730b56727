# Batch & Pipeline Admin

Vue.js 3 + Element Plus + Vue Flow를 사용한 Spring Batch Job 및 CI/CD 파이프라인 관리 시스템

## 🚀 주요 기능

### Spring Batch Job 관리
- **드래그 앤 드롭 인터페이스**: Vue Flow를 사용한 직관적인 Job Step 구성
- **다양한 Step 타입 지원**: Reader, Processor, Writer, Tasklet
- **실시간 Job 상태 모니터링**: Running, Completed, Failed, Pending
- **Job 설정 관리**: JSON 기반 설정 편집
- **Job 실행 및 스케줄링**: 수동 실행 및 스케줄 설정

### CI/CD 파이프라인 관리
- **시각적 파이프라인 구성**: 드래그 앤 드롭으로 파이프라인 단계 구성
- **다양한 Stage 타입**: Build, Test, Deploy, Approval, Custom
- **병렬 실행 지원**: 병렬 처리 가능한 Stage 설정
- **트리거 관리**: Manual, Webhook, Schedule, Git Push 트리거
- **자동 레이아웃**: 의존성 기반 자동 노드 배치

### 관리자 대시보드
- **통계 대시보드**: Job 및 파이프라인 실행 통계
- **최근 활동 모니터링**: 최근 실행된 Job 및 파이프라인 현황
- **상태별 필터링**: 상태별 Job/파이프라인 조회

## 🛠 기술 스택

- **Frontend Framework**: Vue.js 3 (Composition API)
- **UI Library**: Element Plus
- **Flow Editor**: Vue Flow (@vue-flow/core)
- **Build Tool**: Vite
- **Language**: TypeScript
- **State Management**: Pinia
- **Routing**: Vue Router

## 📦 설치 및 실행

### 필수 요구사항
- Node.js 20.19.0+ 또는 22.12.0+
- npm 또는 yarn

### 설치
```bash
# 의존성 설치
npm install

# 개발 서버 실행
npm run dev

# 프로덕션 빌드
npm run build

# 타입 체크
npm run type-check
```

### 개발 서버
```bash
npm run dev
```
브라우저에서 `http://localhost:5173` 접속

## 📁 프로젝트 구조

```
src/
├── components/
│   ├── common/
│   │   └── NodePalette.vue          # 노드 팔레트 컴포넌트
│   └── nodes/
│       ├── BatchStepNode.vue        # Batch Step 노드 컴포넌트
│       └── PipelineStageNode.vue    # Pipeline Stage 노드 컴포넌트
├── router/
│   └── routes.ts                    # 라우터 설정
├── stores/                          # Pinia 스토어 (향후 확장)
├── types/
│   └── index.ts                     # TypeScript 타입 정의
├── views/
│   ├── Dashboard.vue                # 대시보드 페이지
│   ├── BatchJobManager.vue          # Batch Job 관리 페이지
│   └── PipelineManager.vue          # Pipeline 관리 페이지
├── App.vue                          # 메인 앱 컴포넌트
└── main.ts                          # 앱 진입점
```

## 🎯 사용법

### Batch Job 관리
1. **새 Job 생성**: "New Job" 버튼 클릭
2. **Step 추가**: 좌측 팔레트에서 Step 타입을 드래그하여 캔버스에 드롭
3. **Step 연결**: Step 간 연결선을 그어 실행 순서 정의
4. **속성 설정**: Step 클릭 후 우측 패널에서 속성 편집
5. **Job 저장**: "Save" 버튼으로 Job 설정 저장
6. **Job 실행**: "Run" 버튼으로 Job 실행

### Pipeline 관리
1. **새 Pipeline 생성**: "New Pipeline" 버튼 클릭
2. **Stage 추가**: 좌측 팔레트에서 Stage 타입을 드래그하여 캔버스에 드롭
3. **의존성 설정**: Stage 간 연결선으로 의존성 정의
4. **병렬 실행 설정**: Stage 속성에서 병렬 실행 옵션 설정
5. **트리거 설정**: 트리거 다이얼로그에서 실행 조건 설정
6. **Pipeline 저장 및 실행**: "Save" 및 "Run" 버튼 사용

## 🔧 커스터마이징

### 새로운 Step/Stage 타입 추가
1. `src/types/index.ts`에 새 타입 정의
2. `src/components/nodes/`에 새 노드 컴포넌트 생성
3. `src/components/common/NodePalette.vue`에 팔레트 아이템 추가

### 스타일 커스터마이징
- Element Plus 테마 변수 수정
- 각 컴포넌트의 `<style scoped>` 섹션 수정

## 🚧 향후 개발 계획

- [ ] 백엔드 API 연동
- [ ] 실시간 Job/Pipeline 상태 업데이트 (WebSocket)
- [ ] Job/Pipeline 실행 로그 조회
- [ ] 사용자 권한 관리
- [ ] 알림 시스템
- [ ] 성능 모니터링 대시보드
- [ ] 설정 내보내기/가져오기
- [ ] 다국어 지원

## 📄 라이선스

MIT License

## 🤝 기여하기

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📞 문의

프로젝트에 대한 문의사항이나 버그 리포트는 GitHub Issues를 통해 제출해 주세요.
