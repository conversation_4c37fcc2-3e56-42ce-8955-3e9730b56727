<template>
  <el-container class="app-container">
    <!-- Header -->
    <el-header class="app-header">
      <div class="header-content">
        <div class="logo">
          <el-icon><Setting /></el-icon>
          <span>Batch & Pipeline Admin</span>
        </div>
        <div class="header-actions">
          <el-button type="primary" :icon="User" circle />
        </div>
      </div>
    </el-header>

    <el-container>
      <!-- Sidebar -->
      <el-aside width="250px" class="app-sidebar">
        <el-menu
          :default-active="$route.path"
          router
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <el-menu-item index="/dashboard">
            <el-icon><Monitor /></el-icon>
            <span>Dashboard</span>
          </el-menu-item>
          <el-menu-item index="/batch-jobs">
            <el-icon><Operation /></el-icon>
            <span>Batch Jobs</span>
          </el-menu-item>
          <el-menu-item index="/pipelines">
            <el-icon><Connection /></el-icon>
            <span>CI/CD Pipelines</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- Main Content -->
      <el-main class="app-main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { Setting, User, Monitor, Operation, Connection } from '@element-plus/icons-vue'
</script>

<style scoped>
.app-container {
  height: 100vh;
}

.app-header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.logo .el-icon {
  margin-right: 10px;
  font-size: 24px;
}

.app-sidebar {
  background-color: #304156;
}

.sidebar-menu {
  border: none;
  height: 100%;
}

.app-main {
  background-color: #f0f2f5;
  padding: 20px;
}
</style>
