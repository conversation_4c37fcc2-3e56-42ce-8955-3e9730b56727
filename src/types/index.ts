// Batch Job Types
export interface BatchJob {
  id: string
  name: string
  description: string
  status: 'Running' | 'Completed' | 'Failed' | 'Pending'
  steps: BatchStep[]
  schedule?: string
  lastRun?: string
  nextRun?: string
  createdAt: string
  updatedAt: string
}

export interface BatchStep {
  id: string
  name: string
  type: 'reader' | 'processor' | 'writer' | 'tasklet'
  description: string
  config: Record<string, any>
  position: { x: number; y: number }
  connections: string[]
}

// Pipeline Types
export interface Pipeline {
  id: string
  name: string
  description: string
  status: 'Running' | 'Completed' | 'Failed' | 'Pending'
  stages: PipelineStage[]
  triggers: PipelineTrigger[]
  lastRun?: string
  createdAt: string
  updatedAt: string
}

export interface PipelineStage {
  id: string
  name: string
  type: 'build' | 'test' | 'deploy' | 'approval' | 'custom'
  description: string
  config: Record<string, any>
  position: { x: number; y: number }
  dependencies: string[]
  parallel?: boolean
}

export interface PipelineTrigger {
  id: string
  type: 'manual' | 'webhook' | 'schedule' | 'push'
  config: Record<string, any>
}

// Node Types for Vue Flow
export interface FlowNode {
  id: string
  type: string
  position: { x: number; y: number }
  data: {
    label: string
    description?: string
    config?: Record<string, any>
    status?: string
  }
}

export interface FlowEdge {
  id: string
  source: string
  target: string
  type?: string
  animated?: boolean
  style?: Record<string, any>
}
