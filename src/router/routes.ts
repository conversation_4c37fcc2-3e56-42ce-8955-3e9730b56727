import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/Dashboard.vue')
  },
  {
    path: '/batch-jobs',
    name: 'BatchJobs',
    component: () => import('../views/BatchJobManager.vue')
  },
  {
    path: '/pipelines',
    name: 'Pipelines',
    component: () => import('../views/PipelineManager.vue')
  }
]

export default routes
