<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="24">
        <h1>Dashboard</h1>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#409EFF"><Operation /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalJobs }}</div>
              <div class="stat-label">Total Batch Jobs</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#67C23A"><SuccessFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.runningJobs }}</div>
              <div class="stat-label">Running Jobs</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#E6A23C"><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalPipelines }}</div>
              <div class="stat-label">CI/CD Pipelines</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="40" color="#F56C6C"><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.activePipelines }}</div>
              <div class="stat-label">Active Pipelines</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="content-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>Recent Batch Jobs</span>
              <el-button type="primary" size="small" @click="$router.push('/batch-jobs')">
                View All
              </el-button>
            </div>
          </template>
          <el-table :data="recentJobs" style="width: 100%">
            <el-table-column prop="name" label="Job Name" />
            <el-table-column prop="status" label="Status">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastRun" label="Last Run" />
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>Recent Pipelines</span>
              <el-button type="primary" size="small" @click="$router.push('/pipelines')">
                View All
              </el-button>
            </div>
          </template>
          <el-table :data="recentPipelines" style="width: 100%">
            <el-table-column prop="name" label="Pipeline Name" />
            <el-table-column prop="status" label="Status">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastRun" label="Last Run" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Operation, SuccessFilled, Connection, CircleCheck } from '@element-plus/icons-vue'

const stats = ref({
  totalJobs: 24,
  runningJobs: 3,
  totalPipelines: 12,
  activePipelines: 5
})

const recentJobs = ref([
  { name: 'Data Import Job', status: 'Running', lastRun: '2024-01-15 10:30' },
  { name: 'Report Generation', status: 'Completed', lastRun: '2024-01-15 09:15' },
  { name: 'Backup Process', status: 'Failed', lastRun: '2024-01-15 08:00' }
])

const recentPipelines = ref([
  { name: 'Frontend Deploy', status: 'Running', lastRun: '2024-01-15 11:00' },
  { name: 'Backend API', status: 'Completed', lastRun: '2024-01-15 10:45' },
  { name: 'Database Migration', status: 'Pending', lastRun: '2024-01-15 09:30' }
])

const getStatusType = (status: string) => {
  switch (status) {
    case 'Running': return 'warning'
    case 'Completed': return 'success'
    case 'Failed': return 'danger'
    case 'Pending': return 'info'
    default: return ''
  }
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.content-row {
  margin-top: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
