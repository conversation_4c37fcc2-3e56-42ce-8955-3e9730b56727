<template>
  <div 
    :class="['pipeline-stage-node', `stage-${data.type}`, { 'selected': selected }]"
    @click="handleClick"
  >
    <Handle type="target" :position="Position.Top" />
    
    <div class="node-header">
      <el-icon class="node-icon">
        <component :is="getStageIcon(data.type)" />
      </el-icon>
      <span class="node-title">{{ data.label }}</span>
    </div>
    
    <div class="node-content">
      <div class="node-description">{{ data.description }}</div>
      <div v-if="data.status" class="node-status">
        <el-tag :type="getStatusType(data.status)" size="small">
          {{ data.status }}
        </el-tag>
      </div>
      <div v-if="data.parallel" class="node-parallel">
        <el-tag type="info" size="small">Parallel</el-tag>
      </div>
    </div>
    
    <Handle type="source" :position="Position.Bottom" />
  </div>
</template>

<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core'
import { 
  Tools, 
  CircleCheck, 
  Upload, 
  UserFilled,
  Setting 
} from '@element-plus/icons-vue'

interface Props {
  id: string
  data: {
    label: string
    type: 'build' | 'test' | 'deploy' | 'approval' | 'custom'
    description?: string
    status?: string
    parallel?: boolean
    config?: Record<string, any>
  }
  selected?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  click: [id: string]
}>()

const handleClick = () => {
  emit('click', props.id)
}

const getStageIcon = (type: string) => {
  switch (type) {
    case 'build': return Tools
    case 'test': return CircleCheck
    case 'deploy': return Upload
    case 'approval': return UserFilled
    case 'custom': return Setting
    default: return Setting
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'Running': return 'warning'
    case 'Completed': return 'success'
    case 'Failed': return 'danger'
    case 'Pending': return 'info'
    default: return ''
  }
}
</script>

<style scoped>
.pipeline-stage-node {
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  min-width: 160px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.pipeline-stage-node:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.pipeline-stage-node.selected {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.stage-build {
  border-top: 4px solid #E6A23C;
}

.stage-test {
  border-top: 4px solid #67C23A;
}

.stage-deploy {
  border-top: 4px solid #F56C6C;
}

.stage-approval {
  border-top: 4px solid #909399;
}

.stage-custom {
  border-top: 4px solid #409EFF;
}

.node-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.node-icon {
  margin-right: 8px;
  font-size: 16px;
}

.node-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
}

.node-content {
  font-size: 12px;
}

.node-description {
  color: #606266;
  margin-bottom: 6px;
  line-height: 1.4;
}

.node-status {
  margin-bottom: 4px;
}

.node-parallel {
  display: flex;
  justify-content: flex-end;
}
</style>
