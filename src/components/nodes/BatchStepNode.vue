<template>
  <div 
    :class="['batch-step-node', `step-${data.type}`, { 'selected': selected }]"
    @click="handleClick"
  >
    <Handle type="target" :position="Position.Left" />
    
    <div class="node-header">
      <el-icon class="node-icon">
        <component :is="getStepIcon(data.type)" />
      </el-icon>
      <span class="node-title">{{ data.label }}</span>
    </div>
    
    <div class="node-content">
      <div class="node-description">{{ data.description }}</div>
      <div v-if="data.status" class="node-status">
        <el-tag :type="getStatusType(data.status)" size="small">
          {{ data.status }}
        </el-tag>
      </div>
    </div>
    
    <Handle type="source" :position="Position.Right" />
  </div>
</template>

<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core'
import { 
  Download, 
  Setting, 
  Upload, 
  Operation 
} from '@element-plus/icons-vue'

interface Props {
  id: string
  data: {
    label: string
    type: 'reader' | 'processor' | 'writer' | 'tasklet'
    description?: string
    status?: string
    config?: Record<string, any>
  }
  selected?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  click: [id: string]
}>()

const handleClick = () => {
  emit('click', props.id)
}

const getStepIcon = (type: string) => {
  switch (type) {
    case 'reader': return Download
    case 'processor': return Setting
    case 'writer': return Upload
    case 'tasklet': return Operation
    default: return Operation
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'Running': return 'warning'
    case 'Completed': return 'success'
    case 'Failed': return 'danger'
    case 'Pending': return 'info'
    default: return ''
  }
}
</script>

<style scoped>
.batch-step-node {
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  min-width: 180px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.batch-step-node:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.batch-step-node.selected {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.step-reader {
  border-left: 4px solid #67C23A;
}

.step-processor {
  border-left: 4px solid #E6A23C;
}

.step-writer {
  border-left: 4px solid #F56C6C;
}

.step-tasklet {
  border-left: 4px solid #909399;
}

.node-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.node-icon {
  margin-right: 8px;
  font-size: 16px;
}

.node-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
}

.node-content {
  font-size: 12px;
}

.node-description {
  color: #606266;
  margin-bottom: 6px;
  line-height: 1.4;
}

.node-status {
  display: flex;
  justify-content: flex-end;
}
</style>
