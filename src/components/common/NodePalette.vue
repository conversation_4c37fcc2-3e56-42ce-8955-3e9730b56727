<template>
  <div class="node-palette">
    <div class="palette-header">
      <h3>{{ title }}</h3>
    </div>
    <div class="palette-content">
      <div 
        v-for="nodeType in nodeTypes"
        :key="nodeType.type"
        :class="['palette-item', `palette-${nodeType.type}`]"
        draggable="true"
        @dragstart="onDragStart($event, nodeType)"
      >
        <el-icon class="palette-icon">
          <component :is="nodeType.icon" />
        </el-icon>
        <div class="palette-info">
          <div class="palette-name">{{ nodeType.name }}</div>
          <div class="palette-description">{{ nodeType.description }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

import { computed } from 'vue'
<script setup lang="ts">
import { 
  Download, 
  Setting, 
  Upload, 
  Operation,
  Tools,
  CircleCheck,
  UserFilled
} from '@element-plus/icons-vue'

interface NodeType {
  type: string
  name: string
  description: string
  icon: any
  category: 'batch' | 'pipeline'
}

interface Props {
  title: string
  category: 'batch' | 'pipeline'
}

const props = defineProps<Props>()
const emit = defineEmits<{
  dragStart: [event: DragEvent, nodeType: NodeType]
}>()

const batchNodeTypes: NodeType[] = [
  {
    type: 'reader',
    name: 'Reader',
    description: 'Data input step',
    icon: Download,
    category: 'batch'
  },
  {
    type: 'processor',
    name: 'Processor',
    description: 'Data processing step',
    icon: Setting,
    category: 'batch'
  },
  {
    type: 'writer',
    name: 'Writer',
    description: 'Data output step',
    icon: Upload,
    category: 'batch'
  },
  {
    type: 'tasklet',
    name: 'Tasklet',
    description: 'Custom task step',
    icon: Operation,
    category: 'batch'
  }
]

const pipelineNodeTypes: NodeType[] = [
  {
    type: 'build',
    name: 'Build',
    description: 'Build stage',
    icon: Tools,
    category: 'pipeline'
  },
  {
    type: 'test',
    name: 'Test',
    description: 'Test stage',
    icon: CircleCheck,
    category: 'pipeline'
  },
  {
    type: 'deploy',
    name: 'Deploy',
    description: 'Deployment stage',
    icon: Upload,
    category: 'pipeline'
  },
  {
    type: 'approval',
    name: 'Approval',
    description: 'Manual approval',
    icon: UserFilled,
    category: 'pipeline'
  },
  {
    type: 'custom',
    name: 'Custom',
    description: 'Custom stage',
    icon: Setting,
    category: 'pipeline'
  }
]

const nodeTypes = computed(() => {
  return props.category === 'batch' ? batchNodeTypes : pipelineNodeTypes
})

const onDragStart = (event: DragEvent, nodeType: NodeType) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/vueflow', JSON.stringify(nodeType))
    event.dataTransfer.effectAllowed = 'move'
  }
  emit('dragStart', event, nodeType)
}
</script>

<style scoped>
.node-palette {
  width: 250px;
  background: white;
  border-right: 1px solid #e4e7ed;
  height: 100%;
  overflow-y: auto;
}

.palette-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f5f7fa;
}

.palette-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.palette-content {
  padding: 16px;
}

.palette-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.3s ease;
  background: white;
}

.palette-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.palette-item:active {
  cursor: grabbing;
}

.palette-reader {
  border-left: 3px solid #67C23A;
}

.palette-processor {
  border-left: 3px solid #E6A23C;
}

.palette-writer {
  border-left: 3px solid #F56C6C;
}

.palette-tasklet {
  border-left: 3px solid #909399;
}

.palette-build {
  border-left: 3px solid #E6A23C;
}

.palette-test {
  border-left: 3px solid #67C23A;
}

.palette-deploy {
  border-left: 3px solid #F56C6C;
}

.palette-approval {
  border-left: 3px solid #909399;
}

.palette-custom {
  border-left: 3px solid #409EFF;
}

.palette-icon {
  margin-right: 12px;
  font-size: 20px;
  color: #606266;
}

.palette-info {
  flex: 1;
}

.palette-name {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  margin-bottom: 2px;
}

.palette-description {
  font-size: 12px;
  color: #909399;
}
</style>
